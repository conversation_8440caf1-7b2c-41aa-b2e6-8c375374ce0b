using ErrorOr;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using System.Security.Claims;
using System.Text.Encodings.Web;
using Testcontainers.PostgreSql;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Infrastructure.Configurations.Extensions;
using Zify.Settlement.Application.Infrastructure.Persistence;
using Zify.Settlement.Application.Infrastructure.Services;
using Zify.Settlement.Application.Infrastructure.Services.Wallet.Models;

namespace PayPing.Settlement.Api.IntegrationTests;

/// <summary>
/// Test authentication handler that bypasses real JWT validation and provides test claims
/// </summary>
public class TestAuthenticationHandler : AuthenticationHandler<AuthenticationSchemeOptions>
{
    public const string TestScheme = "Test";
    public const int DefaultTestUserId = 12345;

    public TestAuthenticationHandler(IOptionsMonitor<AuthenticationSchemeOptions> options,
        ILoggerFactory logger, UrlEncoder encoder) : base(options, logger, encoder)
    {
    }

    protected override Task<AuthenticateResult> HandleAuthenticateAsync()
    {
        var claims = new[]
        {
            new Claim("sub", DefaultTestUserId.ToString()),
            new Claim("scope", "settlement:write"),
            new Claim("scope", "settlement:read"),
            new Claim("name", "Test User"),
            new Claim("email", "<EMAIL>")
        };

        var identity = new ClaimsIdentity(claims, TestScheme);
        var principal = new ClaimsPrincipal(identity);
        var ticket = new AuthenticationTicket(principal, TestScheme);

        return Task.FromResult(AuthenticateResult.Success(ticket));
    }
}

public class CustomWebApplicationFactory<TProgram>
    : WebApplicationFactory<TProgram>, IAsyncLifetime where TProgram : class
{
    private readonly PostgreSqlContainer _dbContainer = new PostgreSqlBuilder()
        .WithDatabase("test_db")
        .WithUsername("test_user")
        .WithPassword("test_password")
        .Build();

    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        builder.ConfigureServices(services =>
        {
            var descriptor = services.SingleOrDefault(d => d.ServiceType == typeof(DbContextOptions<ApplicationDbContext>));
            if (descriptor != null)
                services.Remove(descriptor);

            var contextDescriptor = services.SingleOrDefault(d => d.ServiceType == typeof(ApplicationDbContext));
            if (contextDescriptor != null)
                services.Remove(contextDescriptor);

            services.AddDbContext<ApplicationDbContext>(options =>
            {
                options.UseNpgsql(_dbContainer.GetConnectionString());
            });

            // Register the interface for dependency injection
            services.AddScoped<IApplicationDbContext>(provider => provider.GetRequiredService<ApplicationDbContext>());

            // Add API Versioning
            services.AddCustomVersioning();

            // Replace authentication with test authentication
            services.AddAuthentication(TestAuthenticationHandler.TestScheme)
                .AddScheme<AuthenticationSchemeOptions, TestAuthenticationHandler>(
                    TestAuthenticationHandler.TestScheme, options => { });

            // Add authorization policies for testing
            services.AddAuthorization(options =>
            {
                options.AddPolicy("read", policy => policy.RequireClaim("scope", "settlement:read"));
                options.AddPolicy("write", policy => policy.RequireClaim("scope", "settlement:write"));
                options.AddPolicy("serviceAdministration", policy => policy.RequireClaim("scope", "settlement:admin"));
                options.AddPolicy("accountingAdministration", policy => policy.RequireClaim("administration", "SettlementAccounting"));
                options.AddPolicy("walletAdministration", policy => policy.RequireClaim("administration", "SettlementWallet"));
            });

            services.AddScoped<ICurrentUserService, CurrentUserService>();
            services.AddTransient<IDateTime, DateTimeService>();

            // Add mock services for testing
            services.AddSingleton<IEventServiceWrapper>(provider =>
            {
                var mock = new Mock<IEventServiceWrapper>();
                mock.Setup(x => x.ValidateTwoStepVerificationAsync(
                    It.IsAny<int>(), It.IsAny<Guid>(), It.IsAny<string>(), It.IsAny<TwoStepType>(), It.IsAny<CancellationToken>()))
                    .ReturnsAsync(Result.Success);
                return mock.Object;
            });

            services.AddSingleton<IWalletService>(provider =>
            {
                var mock = new Mock<IWalletService>();
                mock.Setup(x => x.BlockOrderAmount(
                    It.IsAny<Guid>(), It.IsAny<Guid>(), It.IsAny<decimal>(), It.IsAny<CancellationToken>()))
                    .ReturnsAsync(new FreezeOrderAmountResponse(1000, 0));
                return mock.Object;
            });
        });

        builder.ConfigureAppConfiguration((context, config) =>
        {
            config.AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["Jwt:Key"] = "TestKeyThatIsAtLeast32CharactersLongForTesting!",
                ["Jwt:Issuer"] = "TestIssuer",
                ["Jwt:Audience"] = "TestAudience",
                ["Jwt:ExpiryInMinutes"] = "60"
            });
        });

        builder.UseEnvironment("Testing");
    }

    /// <summary>
    /// Creates an authenticated HTTP client with a specific user ID
    /// </summary>
    /// <param name="userId">The user ID to use for authentication. If null, uses the default test user ID.</param>
    /// <returns>An authenticated HTTP client</returns>
    public HttpClient CreateAuthenticatedClient(int? userId = null)
    {
        return WithWebHostBuilder(builder =>
        {
            builder.ConfigureServices(services =>
            {
                // Remove existing test authentication handler
                var authDescriptor = services.FirstOrDefault(d => d.ServiceType == typeof(TestAuthenticationHandler));
                if (authDescriptor != null)
                    services.Remove(authDescriptor);

                // Add custom test authentication handler with specific user ID
                services.AddSingleton<TestAuthenticationHandlerWithUserId>(provider =>
                    new(provider.GetRequiredService<IOptionsMonitor<AuthenticationSchemeOptions>>(),
                        provider.GetRequiredService<ILoggerFactory>(),
                        provider.GetRequiredService<UrlEncoder>(),
                        userId ?? TestAuthenticationHandler.DefaultTestUserId));

                services.AddAuthentication(TestAuthenticationHandler.TestScheme)
                    .AddScheme<AuthenticationSchemeOptions, TestAuthenticationHandlerWithUserId>(
                        TestAuthenticationHandler.TestScheme, options => { });
            });
        }).CreateClient();
    }

    public async Task InitializeAsync()
    {
        await _dbContainer.StartAsync();
        using var scope = Services.CreateScope();
        var dbContext = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        await dbContext.Database.MigrateAsync();
    }

    public new async Task DisposeAsync()
    {
        await _dbContainer.StopAsync();
    }
}

/// <summary>
/// Test authentication handler that allows specifying a custom user ID
/// </summary>
public class TestAuthenticationHandlerWithUserId(
    IOptionsMonitor<AuthenticationSchemeOptions> options,
    ILoggerFactory logger,
    UrlEncoder encoder,
    int userId) : AuthenticationHandler<AuthenticationSchemeOptions>(options, logger, encoder)
{
    private readonly int _userId = userId;

    protected override Task<AuthenticateResult> HandleAuthenticateAsync()
    {
        var claims = new[]
        {
            new Claim("sub", _userId.ToString()),
            new Claim("scope", "settlement:write"),
            new Claim("scope", "settlement:read"),
            new Claim("name", "Test User"),
            new Claim("email", "<EMAIL>")
        };

        var identity = new ClaimsIdentity(claims, TestAuthenticationHandler.TestScheme);
        var principal = new ClaimsPrincipal(identity);
        var ticket = new AuthenticationTicket(principal, TestAuthenticationHandler.TestScheme);

        return Task.FromResult(AuthenticateResult.Success(ticket));
    }
}
