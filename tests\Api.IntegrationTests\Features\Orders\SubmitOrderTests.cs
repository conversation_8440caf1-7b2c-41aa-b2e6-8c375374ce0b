using ErrorOr;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using Shouldly;
using System.Net.Http.Json;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Domain.ValueObjects;
using Zify.Settlement.Application.Features.Orders.Commands;
using Zify.Settlement.Application.Infrastructure.Persistence;
using Zify.Settlement.Application.Infrastructure.Services.Wallet.Models;

namespace PayPing.Settlement.Api.IntegrationTests.Features.Orders;

public class SubmitOrderTests(CustomWebApplicationFactory<Program> factory)
    : IClassFixture<CustomWebApplicationFactory<Program>>
{
    [Fact]
    public async Task SubmitOrder_WithValidData_ShouldSucceed()
    {
        // Arrange
        var eventServiceMock = new Mock<IEventServiceWrapper>();
        eventServiceMock.Setup(x => x.ValidateTwoStepVerificationAsync(
                It.IsAny<int>(),
                It.IsAny<Guid>(),
                It.IsAny<string>(),
                It.IsAny<TwoStepType>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success);

        var walletServiceMock = new Mock<IWalletService>();
        walletServiceMock.Setup(x => x.BlockOrderAmount(
                It.IsAny<Guid>(),
                It.IsAny<Guid>(),
                It.IsAny<decimal>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new FreezeOrderAmountResponse(1000, 0));

        var client = factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureServices(services =>
            {
                services.AddSingleton(eventServiceMock.Object);
                services.AddSingleton(walletServiceMock.Object);
            });
        }).CreateClient();

        using var scope = factory.Services.CreateScope();
        var dbContext = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

        var order = Order.Create("Test Order");
        var orderDetail = OrderDetail.Create(Iban.Of("**************************"), 1000, 100);
        order.AddDetail(orderDetail);

        dbContext.Orders.Add(order);
        await dbContext.SaveChangesAsync();

        // Act
        var response = await client.PostAsJsonAsync($"/api/v1/{order.Id}/submit-order?twoStepCode=123456", new { });

        // Assert
        response.EnsureSuccessStatusCode();
        var result = await response.Content.ReadFromJsonAsync<SubmitOrderResponse>();
        result.Message.ShouldBe("درخواست شما با موفقیت ثبت شد و درحال پردازش است.");

        var updatedOrder = await dbContext.Orders.AsNoTracking().FirstOrDefaultAsync(o => o.Id == order.Id);
        updatedOrder.Status.ShouldBe(OrderStatus.Submitted);
    }

    [Fact]
    public async Task SubmitOrder_WhenOrderNotFound_ShouldReturnNotFound()
    {
        // Arrange
        var client = factory.CreateClient();
        var nonExistentOrderId = Guid.NewGuid();

        // Act
        var response = await client.PostAsJsonAsync($"/api/v1/{nonExistentOrderId}/submit-order?twoStepCode=123456", new { });

        // Assert
        response.StatusCode.ShouldBe(System.Net.HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task SubmitOrder_WhenTwoStepVerificationFails_ShouldReturnBadRequest()
    {
        // Arrange
        var eventServiceMock = new Mock<IEventServiceWrapper>();
        eventServiceMock.Setup(x => x.ValidateTwoStepVerificationAsync(It.IsAny<int>(), It.IsAny<Guid>(), It.IsAny<string>(), It.IsAny<TwoStepType>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Error.Validation(code: "InvalidCode", description: "کد تایید نامعتبر است"));

        var client = factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureServices(services =>
            {
                services.AddSingleton(eventServiceMock.Object);
            });
        }).CreateClient();

        var orderId = Guid.NewGuid();

        // Act
        var response = await client.PostAsJsonAsync($"/api/v1/{orderId}/submit-order?twoStepCode=wrongcode", new { });

        // Assert
        response.StatusCode.ShouldBe(System.Net.HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task SubmitOrder_WhenWalletServiceFails_ShouldFailOrder()
    {
        // Arrange
        var eventServiceMock = new Mock<IEventServiceWrapper>();
        eventServiceMock.Setup(x => x.ValidateTwoStepVerificationAsync(It.IsAny<int>(), It.IsAny<Guid>(), It.IsAny<string>(), It.IsAny<TwoStepType>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success);

        var walletServiceMock = new Mock<IWalletService>();
        walletServiceMock.Setup(x => x.BlockOrderAmount(It.IsAny<Guid>(), It.IsAny<Guid>(), It.IsAny<decimal>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Error.Failure(description: "خطای در کیف پول تسویه"));

        var client = factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureServices(services =>
            {
                services.AddSingleton(eventServiceMock.Object);
                services.AddSingleton(walletServiceMock.Object);
            });
        }).CreateClient();

        using var scope = factory.Services.CreateScope();
        var dbContext = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

        var order = Order.Create("Test Order");
        var orderDetail = OrderDetail.Create(Iban.Of("**************************"), 1000, 100);
        order.AddDetail(orderDetail);

        dbContext.Orders.Add(order);
        await dbContext.SaveChangesAsync();

        // Act
        var response = await client.PostAsJsonAsync($"/api/v1/{order.Id}/submit-order?twoStepCode=123456", new { });

        // Assert
        response.StatusCode.ShouldBe(System.Net.HttpStatusCode.BadRequest);

        var updatedOrder = await dbContext.Orders.AsNoTracking().FirstOrDefaultAsync(o => o.Id == order.Id);
        updatedOrder.Status.ShouldBe(OrderStatus.Failed);
    }

    [Fact]
    public async Task SubmitOrder_WhenUserIsBanned_ShouldReturnBadRequest()
    {
        // Arrange
        var client = factory.CreateClient();

        using var scope = factory.Services.CreateScope();
        var dbContext = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

        var walletInfo = UserWalletInformation.Create(1, Guid.NewGuid());
        var userConfig = UserConfig.Create(walletInfo, Iban.Of("**************************"));
        userConfig.IsBanned = true;

        dbContext.UserConfigs.Add(userConfig);

        var order = Order.Create("Test Order");
        var orderDetail = OrderDetail.Create(Iban.Of("**************************"), 1000, 100);
        order.AddDetail(orderDetail);

        dbContext.Orders.Add(order);
        await dbContext.SaveChangesAsync();

        // Act
        var response = await client.PostAsJsonAsync($"/api/v1/{order.Id}/submit-order?twoStepCode=123456", new { });

        // Assert
        response.StatusCode.ShouldBe(System.Net.HttpStatusCode.BadRequest);
    }
}